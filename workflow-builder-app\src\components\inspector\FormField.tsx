import React from "react";
import { Node } from "reactflow";
import { WorkflowNodeData, InputDefinition } from "@/types";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { InputRenderer } from "./InputRenderer";
import { getEffectiveInputValue } from "@/utils/inputValueUtils";

interface FormFieldProps {
  inputDef: InputDefinition;
  node: Node<WorkflowNodeData>;
  onConfigChange: (inputName: string, value: any) => void;
  isInputConnected: (inputName: string) => boolean;
  shouldDisableInput: (inputName: string) => boolean;
  getConnectionInfo: (inputName: string) => {
    isConnected: boolean;
    sourceNodeId?: string;
    sourceNodeLabel?: string;
  };
}

/**
 * Reusable form field component for node settings
 */
export function FormField({
  inputDef,
  node,
  onConfigChange,
  isInputConnected,
  shouldDisableInput,
  getConnectionInfo,
}: FormFieldProps) {
  return (
    <div className="pb-2">
      <Label
        htmlFor={`config-${node?.id}-${inputDef.name}`}
        className="flex items-center justify-between text-xs font-medium"
      >
        <span>{inputDef.display_name}</span>
        {inputDef.required && (
          <Badge
            variant="destructive"
            className="h-4 px-1 text-[9px]"
          >
            Required
          </Badge>
        )}
      </Label>
      {inputDef.info && (
        <p className="text-muted-foreground mt-0.5 mb-1.5 text-xs">
          {inputDef.info}
        </p>
      )}
      <InputRenderer
        inputDef={inputDef}
        value={getEffectiveInputValue(node, inputDef)}
        onChange={onConfigChange}
        isDisabled={shouldDisableInput(inputDef.name)}
        isConnected={isInputConnected(inputDef.name)}
        connectionInfo={getConnectionInfo(inputDef.name)}
        nodeId={node.id}
      />
    </div>
  );
}
