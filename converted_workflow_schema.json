{"nodes": [{"id": "AlterMetadataComponent", "server_script_path": "", "server_tools": [{"tool_id": 1, "tool_name": "AlterMetadataComponent", "input_schema": {"predefined_fields": [{"field_name": "input_metadata", "data_type": {"type": "object", "description": "The metadata dictionary to modify. Can be connected from another node or entered directly."}, "required": true}, {"field_name": "updates", "data_type": {"type": "object", "description": "Dictionary of key-value pairs to update or add to the metadata. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "keys_to_remove", "data_type": {"type": "array", "description": "List of keys to remove from the metadata. Can be connected from another node or entered directly."}, "required": false}]}, "output_schema": {"predefined_fields": [{"field_name": "output_metadata", "data_type": {"type": "string", "description": ""}}, {"field_name": "error", "data_type": {"type": "string", "description": ""}}]}}]}, {"id": "CombineTextComponent", "server_script_path": "", "server_tools": [{"tool_id": 1, "tool_name": "CombineTextComponent", "input_schema": {"predefined_fields": [{"field_name": "main_input", "data_type": {"type": "string", "description": "The main text or list to combine. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "num_additional_inputs", "data_type": {"type": "number", "description": "Set the number of additional text inputs to show (1-10)."}, "required": false}, {"field_name": "separator", "data_type": {"type": "string", "description": "The character or string to join the text with."}, "required": false}, {"field_name": "input_1", "data_type": {"type": "string", "description": "Text for input 1. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_2", "data_type": {"type": "string", "description": "Text for input 2. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_3", "data_type": {"type": "string", "description": "Text for input 3. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_4", "data_type": {"type": "string", "description": "Text for input 4. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_5", "data_type": {"type": "string", "description": "Text for input 5. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_6", "data_type": {"type": "string", "description": "Text for input 6. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_7", "data_type": {"type": "string", "description": "Text for input 7. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_8", "data_type": {"type": "string", "description": "Text for input 8. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_9", "data_type": {"type": "string", "description": "Text for input 9. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_10", "data_type": {"type": "string", "description": "Text for input 10. Can be connected from another node or entered directly."}, "required": false}]}, "output_schema": {"predefined_fields": [{"field_name": "result", "data_type": {"type": "string", "description": ""}}, {"field_name": "error", "data_type": {"type": "string", "description": ""}}]}}]}, {"id": "AgenticAI", "server_script_path": "", "server_tools": [{"tool_id": 1, "tool_name": "AgenticAI", "input_schema": {"predefined_fields": [{"field_name": "model_provider", "data_type": {"type": "string", "description": "The AI model provider to use."}, "required": false}, {"field_name": "base_url", "data_type": {"type": "string", "description": "Base URL for the API (leave empty for default provider URL)."}, "required": false}, {"field_name": "api_key", "data_type": {"type": "string", "description": "API key for the model provider. Can be entered directly or referenced from secure storage."}, "required": false}, {"field_name": "model_name", "data_type": {"type": "string", "description": "Select the model to use."}, "required": false}, {"field_name": "temperature", "data_type": {"type": "number", "description": "Controls randomness: 0 is deterministic, higher values are more random."}, "required": false}, {"field_name": "objective", "data_type": {"type": "string", "description": "The task or objective for the agent to accomplish. Can be connected from another node or entered directly."}, "required": true}, {"field_name": "input_variables", "data_type": {"type": "object", "description": "Dictionary of variables to provide to the agent. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "tools", "data_type": {"type": "array", "description": "List of tools available to the agent. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "memory", "data_type": {"type": "string", "description": "Connect a memory object from another node."}, "required": false}, {"field_name": "agent_type", "data_type": {"type": "string", "description": "The type of agent to create."}, "required": false}, {"field_name": "stream", "data_type": {"type": "boolean", "description": "Enable streaming for real-time responses."}, "required": false}]}, "output_schema": {"predefined_fields": [{"field_name": "final_answer", "data_type": {"type": "string", "description": ""}}, {"field_name": "intermediate_steps", "data_type": {"type": "string", "description": ""}}, {"field_name": "updated_memory", "data_type": {"type": "string", "description": ""}}, {"field_name": "error", "data_type": {"type": "string", "description": ""}}]}}]}, {"id": "MessageToDataComponent", "server_script_path": "", "server_tools": [{"tool_id": 1, "tool_name": "MessageToDataComponent", "input_schema": {"predefined_fields": [{"field_name": "input_message", "data_type": {"type": "object", "description": "The Message object to extract fields from. Can be connected from another node or entered directly."}, "required": true}, {"field_name": "fields_to_extract", "data_type": {"type": "array", "description": "List of field names to extract from the message. Leave empty to extract all fields. Can be connected from another node or entered directly."}, "required": false}]}, "output_schema": {"predefined_fields": [{"field_name": "output_data", "data_type": {"type": "string", "description": ""}}, {"field_name": "error", "data_type": {"type": "string", "description": ""}}]}}]}], "transitions": [{"id": "transition-AlterMetadataComponent-*************", "sequence": 1, "transition_type": "initial", "execution_type": "Components", "node_info": {"node_id": "AlterMetadataComponent", "tools_to_use": [{"tool_id": 1, "tool_name": "AlterMetadataComponent", "tool_params": {"items": [{"field_name": "input_metadata", "data_type": "object", "field_value": null}, {"field_name": "updates", "data_type": "object", "field_value": null}, {"field_name": "keys_to_remove", "data_type": "array", "field_value": null}]}}], "input_data": [{"from_transition_id": "transition-start-node", "source_node_id": "Start", "data_type": "string", "mapping": [{"from_field": "flow", "to_field": "input_metadata", "source_handle": "flow", "target_handle": "input_metadata", "edge_id": "reactflow__edge-start-nodeflow-AlterMetadataComponent-*************input_metadata", "mapping_type": "direct", "confidence": "low"}]}], "output_data": [{"to_transition_id": "transition-CombineTextComponent-*************", "target_node_id": "CombineTextComponent", "data_type": "string"}, {"to_transition_id": "transition-AgenticAI-*************", "target_node_id": "AgenticAI", "data_type": "string"}, {"to_transition_id": "transition-MessageToDataComponent-*************", "target_node_id": "MessageToDataComponent", "data_type": "string"}]}, "approval_required": false, "end": false, "conditional_routing": {"cases": [{"condition": {"source": "node_output", "operator": "ends_with", "expected_value": "hello"}, "next_transition": "transition-CombineTextComponent-*************"}, {"condition": {"source": "global_context", "operator": "exists", "expected_value": "", "variable_name": "marketing"}, "next_transition": "transition-AgenticAI-*************"}]}}, {"id": "transition-MessageToDataComponent-*************", "sequence": 2, "transition_type": "standard", "execution_type": "Components", "node_info": {"node_id": "MessageToDataComponent", "tools_to_use": [{"tool_id": 1, "tool_name": "MessageToDataComponent", "tool_params": {"items": [{"field_name": "input_message", "data_type": "object", "field_value": "${input_message}"}, {"field_name": "fields_to_extract", "data_type": "array", "field_value": null}]}}], "input_data": [{"from_transition_id": "transition-AlterMetadataComponent-*************", "source_node_id": "AlterMetadataComponent", "data_type": "string", "mapping": [{"from_field": "condition_3_output", "to_field": "input_message", "source_handle": "condition_3_output", "target_handle": "input_message", "edge_id": "reactflow__edge-ConditionalNode-1748512442609condition_3_output-MessageToDataComponent-*************input_message", "mapping_type": "direct", "confidence": "medium"}]}], "output_data": []}, "approval_required": false, "end": true}, {"id": "transition-AgenticAI-*************", "sequence": 3, "transition_type": "standard", "execution_type": "Components", "node_info": {"node_id": "AgenticAI", "tools_to_use": [{"tool_id": 1, "tool_name": "AgenticAI", "tool_params": {"items": [{"field_name": "model_provider", "data_type": "string", "field_value": null}, {"field_name": "base_url", "data_type": "string", "field_value": null}, {"field_name": "api_key", "data_type": "string", "field_value": null}, {"field_name": "model_name", "data_type": "string", "field_value": null}, {"field_name": "temperature", "data_type": "number", "field_value": null}, {"field_name": "objective", "data_type": "string", "field_value": "${objective}"}, {"field_name": "input_variables", "data_type": "object", "field_value": null}, {"field_name": "tools", "data_type": "array", "field_value": null}, {"field_name": "memory", "data_type": "string", "field_value": null}, {"field_name": "agent_type", "data_type": "string", "field_value": null}, {"field_name": "stream", "data_type": "boolean", "field_value": null}]}}], "input_data": [{"from_transition_id": "transition-AlterMetadataComponent-*************", "source_node_id": "AlterMetadataComponent", "data_type": "string", "mapping": [{"from_field": "condition_2_output", "to_field": "objective", "source_handle": "condition_2_output", "target_handle": "objective", "edge_id": "reactflow__edge-ConditionalNode-1748512442609condition_2_output-AgenticAI-*************objective", "mapping_type": "direct", "confidence": "medium"}]}], "output_data": []}, "approval_required": false, "end": true}, {"id": "transition-CombineTextComponent-*************", "sequence": 4, "transition_type": "standard", "execution_type": "Components", "node_info": {"node_id": "CombineTextComponent", "tools_to_use": [{"tool_id": 1, "tool_name": "CombineTextComponent", "tool_params": {"items": [{"field_name": "main_input", "data_type": "string", "field_value": "${main_input}"}, {"field_name": "num_additional_inputs", "data_type": "number", "field_value": null}, {"field_name": "separator", "data_type": "string", "field_value": null}, {"field_name": "input_1", "data_type": "string", "field_value": null}, {"field_name": "input_2", "data_type": "string", "field_value": null}, {"field_name": "input_3", "data_type": "string", "field_value": null}, {"field_name": "input_4", "data_type": "string", "field_value": null}, {"field_name": "input_5", "data_type": "string", "field_value": null}, {"field_name": "input_6", "data_type": "string", "field_value": null}, {"field_name": "input_7", "data_type": "string", "field_value": null}, {"field_name": "input_8", "data_type": "string", "field_value": null}, {"field_name": "input_9", "data_type": "string", "field_value": null}, {"field_name": "input_10", "data_type": "string", "field_value": null}]}}], "input_data": [{"from_transition_id": "transition-AlterMetadataComponent-*************", "source_node_id": "AlterMetadataComponent", "data_type": "string", "mapping": [{"from_field": "condition_1_output", "to_field": "main_input", "source_handle": "condition_1_output", "target_handle": "main_input", "edge_id": "reactflow__edge-ConditionalNode-1748512442609condition_1_output-CombineTextComponent-*************main_input", "mapping_type": "direct", "confidence": "medium"}]}], "output_data": []}, "approval_required": false, "end": true}]}