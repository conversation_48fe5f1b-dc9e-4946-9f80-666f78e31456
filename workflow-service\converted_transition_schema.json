{"nodes": [{"id": "AgenticAI", "server_script_path": "", "server_tools": [{"tool_id": 1, "tool_name": "AgenticAI", "input_schema": {"predefined_fields": [{"field_name": "model_provider", "data_type": {"type": "string", "description": "The AI model provider to use."}, "required": false}, {"field_name": "base_url", "data_type": {"type": "string", "description": "Base URL for the API (leave empty for default provider URL)."}, "required": false}, {"field_name": "api_key", "data_type": {"type": "string", "description": "API key for the model provider. Can be entered directly or referenced from secure storage."}, "required": false}, {"field_name": "model_name", "data_type": {"type": "string", "description": "Select the model to use."}, "required": false}, {"field_name": "temperature", "data_type": {"type": "number", "description": "Controls randomness: 0 is deterministic, higher values are more random."}, "required": false}, {"field_name": "objective", "data_type": {"type": "string", "description": "The task or objective for the agent to accomplish. Can be connected from another node or entered directly."}, "required": true}, {"field_name": "input_variables", "data_type": {"type": "object", "description": "Dictionary of variables to provide to the agent. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "tools", "data_type": {"type": "array", "description": "List of tools available to the agent. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "memory", "data_type": {"type": "string", "description": "Connect a memory object from another node."}, "required": false}, {"field_name": "agent_type", "data_type": {"type": "string", "description": "The type of agent to create."}, "required": false}, {"field_name": "stream", "data_type": {"type": "boolean", "description": "Enable streaming for real-time responses."}, "required": false}]}, "output_schema": {"predefined_fields": [{"field_name": "final_answer", "data_type": {"type": "string", "description": ""}}, {"field_name": "intermediate_steps", "data_type": {"type": "string", "description": ""}}, {"field_name": "updated_memory", "data_type": {"type": "string", "description": ""}}, {"field_name": "error", "data_type": {"type": "string", "description": ""}}]}}]}, {"id": "ApiRequestNode", "server_script_path": "", "server_tools": [{"tool_id": 1, "tool_name": "ApiRequestNode", "input_schema": {"predefined_fields": [{"field_name": "url", "data_type": {"type": "string", "description": "The URL to make the request to."}, "required": true}, {"field_name": "method", "data_type": {"type": "string", "description": "The HTTP method to use for the request."}, "required": false}, {"field_name": "query_params", "data_type": {"type": "object", "description": "Key-value pairs to append to the URL query string (optional)."}, "required": false}, {"field_name": "headers", "data_type": {"type": "object", "description": "Key-value pairs for request headers (optional)."}, "required": false}, {"field_name": "body", "data_type": {"type": "object", "description": "Key-value dictionary for the request body (used for POST, PUT, PATCH). Will be JSON serialized."}, "required": false}, {"field_name": "timeout", "data_type": {"type": "number", "description": "Maximum time to wait for a response."}, "required": false}, {"field_name": "follow_redirects", "data_type": {"type": "boolean", "description": "Automatically follow HTTP redirects (e.g., 301, 302)."}, "required": false}, {"field_name": "save_to_file", "data_type": {"type": "boolean", "description": "Save response body to a temporary file instead of returning content directly. 'Result' output will be the file path."}, "required": false}, {"field_name": "output_format", "data_type": {"type": "string", "description": "'auto': Try JSON, fallback to text. 'json': Force JSON parsing (error if invalid). 'text': Return as text. 'bytes': Return raw bytes. 'file_path': Always save to file (requires 'Save Response to File'). 'metadata_dict': Return dict with status, headers, body/path, error."}, "required": false}, {"field_name": "raise_on_error", "data_type": {"type": "boolean", "description": "Stop workflow execution if an HTTP error status (4xx, 5xx) is received."}, "required": false}]}, "output_schema": {"predefined_fields": [{"field_name": "result", "data_type": {"type": "string", "description": ""}}, {"field_name": "status_code", "data_type": {"type": "string", "description": ""}}, {"field_name": "response_headers", "data_type": {"type": "string", "description": ""}}, {"field_name": "error", "data_type": {"type": "string", "description": ""}}]}}]}, {"id": "AlterMetadataComponent", "server_script_path": "", "server_tools": [{"tool_id": 1, "tool_name": "AlterMetadataComponent", "input_schema": {"predefined_fields": [{"field_name": "input_metadata", "data_type": {"type": "object", "description": "The metadata dictionary to modify. Can be connected from another node or entered directly."}, "required": true}, {"field_name": "updates", "data_type": {"type": "object", "description": "Dictionary of key-value pairs to update or add to the metadata. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "keys_to_remove", "data_type": {"type": "array", "description": "List of keys to remove from the metadata. Can be connected from another node or entered directly."}, "required": false}]}, "output_schema": {"predefined_fields": [{"field_name": "output_metadata", "data_type": {"type": "string", "description": ""}}, {"field_name": "error", "data_type": {"type": "string", "description": ""}}]}}]}, {"id": "CombineTextComponent", "server_script_path": "", "server_tools": [{"tool_id": 1, "tool_name": "CombineTextComponent", "input_schema": {"predefined_fields": [{"field_name": "main_input", "data_type": {"type": "string", "description": "The main text or list to combine. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "num_additional_inputs", "data_type": {"type": "number", "description": "Set the number of additional text inputs to show (1-10)."}, "required": false}, {"field_name": "separator", "data_type": {"type": "string", "description": "The character or string to join the text with."}, "required": false}, {"field_name": "input_1", "data_type": {"type": "string", "description": "Text for input 1. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_2", "data_type": {"type": "string", "description": "Text for input 2. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_3", "data_type": {"type": "string", "description": "Text for input 3. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_4", "data_type": {"type": "string", "description": "Text for input 4. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_5", "data_type": {"type": "string", "description": "Text for input 5. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_6", "data_type": {"type": "string", "description": "Text for input 6. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_7", "data_type": {"type": "string", "description": "Text for input 7. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_8", "data_type": {"type": "string", "description": "Text for input 8. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_9", "data_type": {"type": "string", "description": "Text for input 9. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_10", "data_type": {"type": "string", "description": "Text for input 10. Can be connected from another node or entered directly."}, "required": false}]}, "output_schema": {"predefined_fields": [{"field_name": "result", "data_type": {"type": "string", "description": ""}}, {"field_name": "error", "data_type": {"type": "string", "description": ""}}]}}]}], "transitions": [{"id": "transition-CombineTextComponent-1748506284632", "sequence": 1, "transition_type": "initial", "execution_type": "Components", "node_info": {"node_id": "CombineTextComponent", "tools_to_use": [{"tool_id": 1, "tool_name": "CombineTextComponent", "tool_params": {"items": [{"field_name": "main_input", "data_type": "string", "field_value": null}, {"field_name": "num_additional_inputs", "data_type": "number", "field_value": null}, {"field_name": "separator", "data_type": "string", "field_value": null}, {"field_name": "input_1", "data_type": "string", "field_value": null}, {"field_name": "input_2", "data_type": "string", "field_value": null}, {"field_name": "input_3", "data_type": "string", "field_value": null}, {"field_name": "input_4", "data_type": "string", "field_value": null}, {"field_name": "input_5", "data_type": "string", "field_value": null}, {"field_name": "input_6", "data_type": "string", "field_value": null}, {"field_name": "input_7", "data_type": "string", "field_value": null}, {"field_name": "input_8", "data_type": "string", "field_value": null}, {"field_name": "input_9", "data_type": "string", "field_value": null}, {"field_name": "input_10", "data_type": "string", "field_value": null}]}}], "input_data": [{"from_transition_id": "transition-start-node", "source_node_id": "Start", "data_type": "string", "mapping": [{"from_field": "flow", "to_field": "main_input", "source_handle": "flow", "target_handle": "main_input", "edge_id": "reactflow__edge-start-nodeflow-CombineTextComponent-1748506284632main_input", "mapping_type": "direct", "confidence": "low"}]}], "output_data": [{"to_transition_id": "transition-AgenticAI-*************", "target_node_id": "AgenticAI", "data_type": "string"}, {"to_transition_id": "transition-ApiRequestNode-*************", "target_node_id": "ApiRequestNode", "data_type": "string"}, {"to_transition_id": "transition-AlterMetadataComponent-*************", "target_node_id": "AlterMetadataComponent", "data_type": "string"}]}, "approval_required": false, "end": false, "conditional_routing": {"cases": [{"condition": {"source": "node_output", "operator": "equals", "expected_value": ""}, "next_transition": "transition-AgenticAI-*************"}, {"condition": {"source": "node_output", "operator": "equals", "expected_value": ""}, "next_transition": "transition-ApiRequestNode-*************"}], "default_transition": "transition-AlterMetadataComponent-*************"}}, {"id": "transition-ApiRequestNode-*************", "sequence": 2, "transition_type": "standard", "execution_type": "Components", "node_info": {"node_id": "ApiRequestNode", "tools_to_use": [{"tool_id": 1, "tool_name": "ApiRequestNode", "tool_params": {"items": [{"field_name": "url", "data_type": "string", "field_value": null}, {"field_name": "method", "data_type": "string", "field_value": null}, {"field_name": "query_params", "data_type": "object", "field_value": "${query_params}"}, {"field_name": "headers", "data_type": "object", "field_value": null}, {"field_name": "body", "data_type": "object", "field_value": null}, {"field_name": "timeout", "data_type": "number", "field_value": null}, {"field_name": "follow_redirects", "data_type": "boolean", "field_value": null}, {"field_name": "save_to_file", "data_type": "boolean", "field_value": null}, {"field_name": "output_format", "data_type": "string", "field_value": null}, {"field_name": "raise_on_error", "data_type": "boolean", "field_value": null}]}}], "input_data": [{"from_transition_id": "transition-CombineTextComponent-1748506284632", "source_node_id": "CombineTextComponent", "data_type": "string", "mapping": [{"from_field": "condition_2_output", "to_field": "query_params", "source_handle": "condition_2_output", "target_handle": "query_params", "edge_id": "reactflow__edge-ConditionalNode-1748495878571condition_2_output-ApiRequestNode-*************query_params", "mapping_type": "direct", "confidence": "medium"}]}], "output_data": []}, "approval_required": false, "end": true}, {"id": "transition-AlterMetadataComponent-*************", "sequence": 3, "transition_type": "standard", "execution_type": "Components", "node_info": {"node_id": "AlterMetadataComponent", "tools_to_use": [{"tool_id": 1, "tool_name": "AlterMetadataComponent", "tool_params": {"items": [{"field_name": "input_metadata", "data_type": "object", "field_value": "${input_metadata}"}, {"field_name": "updates", "data_type": "object", "field_value": null}, {"field_name": "keys_to_remove", "data_type": "array", "field_value": null}]}}], "input_data": [{"from_transition_id": "transition-CombineTextComponent-1748506284632", "source_node_id": "CombineTextComponent", "data_type": "string", "mapping": [{"from_field": "default_output", "to_field": "input_metadata", "source_handle": "default_output", "target_handle": "input_metadata", "edge_id": "reactflow__edge-ConditionalNode-1748495878571default_output-AlterMetadataComponent-*************input_metadata", "mapping_type": "direct", "confidence": "medium"}]}], "output_data": []}, "approval_required": false, "end": true}, {"id": "transition-AgenticAI-*************", "sequence": 4, "transition_type": "standard", "execution_type": "Components", "node_info": {"node_id": "AgenticAI", "tools_to_use": [{"tool_id": 1, "tool_name": "AgenticAI", "tool_params": {"items": [{"field_name": "model_provider", "data_type": "string", "field_value": null}, {"field_name": "base_url", "data_type": "string", "field_value": null}, {"field_name": "api_key", "data_type": "string", "field_value": null}, {"field_name": "model_name", "data_type": "string", "field_value": null}, {"field_name": "temperature", "data_type": "number", "field_value": null}, {"field_name": "objective", "data_type": "string", "field_value": "${objective}"}, {"field_name": "input_variables", "data_type": "object", "field_value": null}, {"field_name": "tools", "data_type": "array", "field_value": null}, {"field_name": "memory", "data_type": "string", "field_value": null}, {"field_name": "agent_type", "data_type": "string", "field_value": null}, {"field_name": "stream", "data_type": "boolean", "field_value": null}]}}], "input_data": [{"from_transition_id": "transition-CombineTextComponent-1748506284632", "source_node_id": "CombineTextComponent", "data_type": "string", "mapping": [{"from_field": "condition_1_output", "to_field": "objective", "source_handle": "condition_1_output", "target_handle": "objective", "edge_id": "reactflow__edge-ConditionalNode-1748495878571condition_1_output-AgenticAI-*************objective", "mapping_type": "direct", "confidence": "medium"}]}], "output_data": []}, "approval_required": false, "end": true}]}