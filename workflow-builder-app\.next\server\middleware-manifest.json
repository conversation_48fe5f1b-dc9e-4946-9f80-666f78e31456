{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_94a4b977._.js", "server/edge/chunks/[root of the server]__1daf8b13._.js", "server/edge/chunks/edge-wrapper_0cc0fab8.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next|_vercel|.*\\.[\\w]+$).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next|_vercel|.*\\.[\\w]+$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "a+lIWRKRxvnVQaEcqhJqHOiJoD1n5nRfxss0mxpJlgU=", "__NEXT_PREVIEW_MODE_ID": "35bc5ee54a06b7b37ff6ec9ce4f1161b", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "fb11e7d99eb9df4659eb96257cc862cc8e2497447e71d273eada73e7866b68d7", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "cc3b28abfb56e8daa1bdd28d7b7d4a2e337b9a3916ccf2cf69c0481f583d000b"}}}, "sortedMiddleware": ["/"], "functions": {}}