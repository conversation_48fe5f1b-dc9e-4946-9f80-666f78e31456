"use client";

import React, { memo, useState } from "react";
import { format } from "date-fns";
import { Calendar, Trash2, MoreHorizontal, Pencil, Workflow } from "lucide-react";
import { WorkflowDetails, WorkflowSummary, deleteWorkflow, updateWorkflowMetadata } from "../api";
import { Card, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";

interface WorkflowCardProps {
  workflow: WorkflowDetails | WorkflowSummary;
  onClick: (workflow: WorkflowDetails | WorkflowSummary) => void;
  onDelete?: (workflowId: string) => void;
}

// Format date for display in "Month DD, YYYY" format
const formatDate = (dateString: string) => {
  try {
    return format(new Date(dateString), "MMM d, yyyy");
  } catch (e) {
    return "Unknown date";
  }
};

// Get status display properties
const getStatusProperties = (status: string | undefined) => {
  if (!status) return { label: "Inactive", active: false };

  const isActive = status.toLowerCase() === "active";
  return {
    label: isActive ? "Active" : "Inactive",
    active: isActive,
  };
};

const WorkflowCard: React.FC<WorkflowCardProps> = ({ workflow, onClick }) => {
  // Check if the workflow is a WorkflowSummary (has workflow property) or a direct WorkflowDetails
  const workflowData = "workflow" in workflow ? workflow.workflow : workflow;
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [editTitle, setEditTitle] = useState(workflowData?.name || "");
  const [editDescription, setEditDescription] = useState(workflowData?.description || "");

  // Handle delete menu item click
  const handleDeleteClick = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent card click event
    setIsDeleteDialogOpen(true);
  };

  // Handle edit menu item click
  const handleEditClick = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent card click event
    setEditTitle(workflowData?.name || "");
    setEditDescription(workflowData?.description || "");
    setIsEditDialogOpen(true);
  };

  // Handle delete confirmation
  const handleDeleteConfirm = async () => {
    if (!workflowData?.id) return;

    setIsDeleting(true);
    try {
      const result = await deleteWorkflow(workflowData.id);

      if (result.success) {
        toast.success(result.message || "Workflow deleted successfully");
        // Refresh the page after a short delay to show the updated list
        setTimeout(() => {
          window.location.reload();
        }, 1000);
      } else {
        toast.error("Failed to delete workflow");
      }
    } catch (error) {
      console.error("Error deleting workflow:", error);
      toast.error(
        error instanceof Error ? error.message : "An error occurred while deleting the workflow",
      );
    } finally {
      setIsDeleting(false);
      setIsDeleteDialogOpen(false);
    }
  };

  // Handle update workflow
  const handleUpdateWorkflow = async () => {
    if (!workflowData?.id) return;

    setIsUpdating(true);
    try {
      await updateWorkflowMetadata(workflowData.id, {
        name: editTitle,
        description: editDescription,
      });

      toast.success("Workflow updated successfully");

      // Refresh the page after a short delay to show the updated list
      setTimeout(() => {
        window.location.reload();
      }, 1000);
    } catch (error) {
      console.error("Error updating workflow:", error);
      toast.error(
        error instanceof Error ? error.message : "An error occurred while updating the workflow",
      );
    } finally {
      setIsUpdating(false);
      setIsEditDialogOpen(false);
    }
  };

  return (
    <>
      <Card
        className="group relative w-full cursor-pointer transition-all hover:shadow-lg"
        style={{ backgroundColor: "#1E1E1E" }}
        onClick={() => onClick(workflow)}
        onKeyDown={(e) => {
          // Handle keyboard navigation - Enter or Space to select
          if (e.key === "Enter" || e.key === " ") {
            e.preventDefault();
            onClick(workflow);
          }
        }}
        tabIndex={0}
        role="button"
        aria-label={`Select workflow: ${workflowData?.name || "Untitled Workflow"}`}
      >
        <CardHeader className="relative px-3 py-3">
          {/* Kebab menu - positioned absolutely in top right corner */}
          <div
            className="absolute right-2 z-10"
            style={{ top: "-6px" }}
            onClick={(e) => e.stopPropagation()}
            role="menu"
            aria-label="Workflow options"
          >
            <DropdownMenu>
              <DropdownMenuTrigger className="rounded-full p-1.5 transition-colors hover:bg-gray-600">
                <MoreHorizontal className="h-4 w-4 text-white" />
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="border-brand-stroke bg-brand-card w-40">
                <DropdownMenuItem
                  className="text-brand-primary-font hover:bg-brand-card-hover flex cursor-pointer items-center gap-2 py-2"
                  onClick={handleEditClick}
                >
                  <Pencil className="h-4 w-4" />
                  <span>Edit</span>
                </DropdownMenuItem>
                <DropdownMenuItem
                  className="text-brand-unpublish hover:bg-brand-card-hover flex cursor-pointer items-center gap-2 py-2"
                  onClick={handleDeleteClick}
                >
                  <Trash2 className="h-4 w-4" />
                  <span>Delete</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          {/* Main content layout */}
          <div className="flex items-center justify-between">
            {/* Left side: Icon and Name/Description */}
            <div className="flex flex-1 items-center gap-3 pr-4">
              {/* Workflow Icon */}
              <div className="bg-brand-primary/20 flex h-10 w-10 flex-shrink-0 items-center justify-center rounded-full">
                <Workflow className="text-brand-primary h-5 w-5" />
              </div>

              {/* Name and Description */}
              <div className="min-w-0 flex-1">
                <CardTitle className="font-primary text-base font-semibold text-white">
                  {workflowData?.name || "Untitled Workflow"}
                </CardTitle>
                <CardDescription className="font-secondary text-xs text-gray-300">
                  {workflowData?.description || "No description provided"}
                </CardDescription>
                {/* Date below description */}
                <div className="mt-1 flex items-center gap-1 text-xs text-gray-400">
                  <Calendar className="h-3 w-3" />
                  <span>
                    {workflowData?.created_at ? formatDate(workflowData.created_at) : "Unknown"}
                  </span>
                </div>
              </div>
            </div>

            {/* Right side: Status Toggle */}
            <div className="flex flex-shrink-0 items-center">
              <div
                className={`relative inline-flex h-5 w-9 items-center rounded-full transition-colors ${
                  getStatusProperties(workflowData?.status).active
                    ? "bg-[rgba(123,90,255,1)]"
                    : "bg-gray-500"
                }`}
                role="switch"
                aria-checked={getStatusProperties(workflowData?.status).active}
                aria-label={`Toggle workflow status: currently ${getStatusProperties(workflowData?.status).label}`}
                onClick={(e) => {
                  e.stopPropagation();
                  // Status toggle functionality would go here
                }}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    getStatusProperties(workflowData?.status).active
                      ? "translate-x-5"
                      : "translate-x-1"
                  }`}
                />
              </div>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Delete confirmation dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent className="border-brand-stroke bg-brand-card">
          <AlertDialogHeader>
            <AlertDialogTitle className="font-primary text-brand-primary-font">
              Delete Workflow
            </AlertDialogTitle>
            <AlertDialogDescription className="font-secondary text-brand-secondary-font">
              Are you sure you want to delete this workflow? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel
              disabled={isDeleting}
              className="border-brand-stroke text-brand-primary-font hover:bg-brand-clicked"
            >
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteConfirm}
              disabled={isDeleting}
              className="bg-brand-unpublish text-brand-white-text hover:bg-brand-unpublish/90"
            >
              {isDeleting ? "Deleting..." : "Delete"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Edit workflow dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="border-brand-stroke bg-brand-card sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle className="font-primary text-brand-primary-font">
              Edit Workflow
            </DialogTitle>
            <DialogDescription className="font-secondary text-brand-secondary-font">
              Update the title and description of your workflow.
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="title" className="text-brand-primary-font">
                Title
              </Label>
              <Input
                id="title"
                value={editTitle}
                onChange={(e) => setEditTitle(e.target.value)}
                className="border-brand-stroke bg-brand-card-hover text-brand-primary-font"
                placeholder="Enter workflow title"
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="description" className="text-brand-primary-font">
                Description
              </Label>
              <Textarea
                id="description"
                value={editDescription}
                onChange={(e) => setEditDescription(e.target.value)}
                className="border-brand-stroke bg-brand-card-hover text-brand-primary-font min-h-[100px]"
                placeholder="Enter workflow description"
              />
            </div>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsEditDialogOpen(false)}
              className="border-brand-stroke text-brand-primary-font hover:bg-brand-clicked"
              disabled={isUpdating}
            >
              Cancel
            </Button>
            <Button
              onClick={handleUpdateWorkflow}
              className="bg-brand-primary text-brand-white-text hover:bg-brand-primary/90"
              disabled={isUpdating}
            >
              {isUpdating ? "Saving..." : "Save changes"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

// Memoize the component to prevent unnecessary re-renders
export default memo(WorkflowCard);
